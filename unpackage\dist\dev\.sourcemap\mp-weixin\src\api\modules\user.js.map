{"version": 3, "file": "user.js", "sources": ["src/api/modules/user.ts"], "sourcesContent": ["/**\n * 用户相关API接口\n */\nimport http from '../../utils/request';\nimport type { UserInfo, LoginParams, RegisterParams } from '../../types/api';\n\n/**\n * 微信登录\n */\nexport function wxLogin(params: LoginParams) {\n  return http.post<UserInfo>('/auth/wechat-login', params);\n}\n\n/**\n * 提交用户注册信息\n */\nexport function submitUserInfo(params: RegisterParams) {\n  return http.post<boolean>('/user/register', params);\n}\n\n/**\n * 获取用户信息\n */\nexport function getUserInfo() {\n  return http.get<UserInfo>('/user/profile');\n}\n\n/**\n * 上传用户头像\n */\nexport function uploadAvatar(file: File) {\n  return http.upload<string>('/user/upload-avatar', { file });\n}\n"], "names": ["http"], "mappings": ";;AASO,SAAS,QAAQ,QAAqB;AACpC,SAAAA,uBAAK,KAAe,sBAAsB,MAAM;AACzD;AAKO,SAAS,eAAe,QAAwB;AAC9C,SAAAA,uBAAK,KAAc,kBAAkB,MAAM;AACpD;;;"}